import 'package:neorevv/src/core/enum/user_role.dart';

class User {
  final String name;
  final String email;
  final String phone;
  final String image;
  final UserRole role;
  final String? token;

  User({
    required this.name,
    required this.email,
    required this.phone,
    required this.image,
    required this.role,
    this.token,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      name: json['name'] ?? "",
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      image: json['image'] ?? '',
      role: stringToUserRole(json['role'] ?? 'Platform Admin'),
      token:
          json['jwt'], // Add token field to the constructor and JSON parsing logic
    );
  }
}
