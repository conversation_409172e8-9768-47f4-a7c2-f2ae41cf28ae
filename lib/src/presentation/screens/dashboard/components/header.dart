import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/shared/components/elevated_button.dart';
import '../../agent/agents_screen.dart';
import '../dashboard_screen.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/enum/user_role.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../sales/sales_review_doc_screen.dart';
import '../../../../domain/models/user.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import 'mobile_drawer.dart';

class Header extends HookWidget {
  final String selectedTab;
  final List<Widget> navItems;
  Header({super.key, required this.selectedTab, this.navItems = const []});

  // TODO: Remove after API integration
  final User user = User(
    name: "Nabil",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.platformOwner,
  );

  final List<String> headerItems = [
    dashboardTab,
    brokersTab,
    agentsTab,
    salesTab,
    commissionTab,
    reportsTab,
  ];

  final ValueNotifier<String> _selectedTab = ValueNotifier<String>('');

  // Getter for the mobile drawer
  Widget get mobileDrawer => MobileDrawer(
    user: user,
    selectedTab: _selectedTab,
    onTabSelected: (index) {},
    selectedTabIndex: 0,
    tabs: [],
  );

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      _selectedTab.value = selectedTab;
    }, [selectedTab]);

    return ValueListenableBuilder(
      valueListenable: _selectedTab,
      builder: (context, value, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding / 2,
            vertical: defaultPadding / 2,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(12)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                spreadRadius: 0,
                blurRadius: 2,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Mobile menu button
              if (Responsive.showDrawer(context))
                IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                ),
              const SizedBox(width: 8),

              // Logo
              Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),

              // Desktop navigation items
              if (!Responsive.showDrawer(context)) ...[
                const SizedBox(height: 20),
                ...navItems,

                // _buildNavItem(
                //   context,
                //   dashboardTab,
                //   isSelected: _selectedTab.value == dashboardTab,
                // ),
                // _buildNavItem(
                //   context,
                //   brokersTab,
                //   isSelected: _selectedTab.value == brokersTab,
                // ),
                // _buildNavItem(
                //   context,
                //   agentsTab,
                //   isSelected: _selectedTab.value == agentsTab,
                // ),
                // _buildNavItem(
                //   context,
                //   salesTab,
                //   isSelected: _selectedTab.value == salesTab,
                // ),
                // _buildNavItem(
                //   context,
                //   commissionTab,
                //   isSelected: _selectedTab.value == commissionTab,
                // ),
                // if (user.role != UserRole.agent)
                //   _buildNavItem(
                //     context,
                //     reportsTab,
                //     isSelected: _selectedTab.value == reportsTab,
                //   ),
                const Spacer(),
                // TODO: update after API integration
                // if (user.role != UserRole.platformOwner)
                _buildAddNewBtn(context),
                const SizedBox(width: defaultPadding),

                _headerIcon(Icons.notifications_outlined),
                const SizedBox(width: defaultPadding),
                _headerIcon(Icons.settings_outlined),
                const SizedBox(width: defaultPadding),
              ],
              // Responsive.showDrawer(context)
              //     ? const Spacer()
              //     : const SizedBox.shrink(),

              // Desktop action buttons and profile
              if (!Responsive.isMobile(context)) ...[
                _buildProfileInfo(context),
              ],

              // Mobile profile (simplified)
              if (Responsive.isMobile(context)) ...[
                // const Spacer(),
                const SizedBox(width: defaultPadding),
                CircleAvatar(
                  backgroundImage: AssetImage(user.image),
                  radius: 16,
                ),
                const SizedBox(width: 8),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _headerIcon(IconData icon) {
    return Container(
      //background color-light grey, circle shape
      decoration: BoxDecoration(
        color: AppTheme.headerIconBgColor,
        shape: BoxShape.circle,
      ),

      child: Padding(padding: const EdgeInsets.all(5.0), child: Icon(icon)),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: () {
          _selectedTab.value = title;
          if (title == dashboardTab) {
            // Navigator.pushReplacementNamed(context, '/route-name');
            // Navigator.pushReplacementNamed(context, '/dashboard');
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (context) => DashboardScreen()),
              (Route<dynamic> route) => false, // Removes all previous routes
            );

            //do nothing
          } else if (title == brokersTab) {
            //navigate to brokers
          } else if (title == agentsTab) {
            Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (context) => AgentsScreen()));
            //navigate to agents
          } else if (title == salesTab) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    SalesReviewDocScreen(enableEditing: false),
              ),
            );

            //navigate to sales
          } else if (title == commissionTab) {
            //navigate to commission
          } else if (title == reportsTab) {
            //navigate to reports
          }
        },
        child: Container(
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(
            // color: Colors.red,
            //     width: 2,
            //   ),
            // ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor)
                    : AppFonts.mediumTextStyle(14, color: Colors.black),
              ),
              // if (isSelected)
              //   Container(
              //     // height: 2,
              //     // width: 90,
              //     child: const TriangleIndicator(),
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewBtn(BuildContext context) {
    return AppButton(
      label: addNewButton,
      onPressed: () {
        _selectedTab.value = addNewButton;
        Navigator.pushNamed(context, '/register-broker');
      },
    );

    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
      ),
      onPressed: () {
        _selectedTab.value = addNewButton;
        Navigator.pushNamed(context, '/register-broker');
      },
      icon: const Icon(Icons.add),
      label: const Text(addNewButton),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(backgroundImage: AssetImage(user.image)),
        const SizedBox(width: defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(user.name, style: TextStyle(fontWeight: FontWeight.bold)),
              Text(userRoleToString(user.role), style: TextStyle(fontSize: 12)),
            ],
          ),
        if (Responsive.isDesktop(context))
          const Icon(Icons.keyboard_arrow_down),
      ],
    );
  }
}
