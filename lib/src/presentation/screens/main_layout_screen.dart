import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/shared/components/breadcrumb_navigation.dart';
import '/src/presentation/shared/components/header.dart';
import '/src/presentation/screens/agent/components/agents_table.dart';
import '/src/presentation/screens/sales/sales_review_doc_screen.dart';
import '/src/presentation/screens/dashboard/components/mobile_drawer.dart';

import '../../core/config/app_strings.dart';
import '../../core/config/constants.dart';
import '../../core/config/responsive.dart';
import '../../core/theme/app_fonts.dart';
import '../../core/theme/app_theme.dart';
import '../../core/enum/user_role.dart';
import '../../domain/models/user.dart';
import 'dashboard/components/dashboard_content.dart';

class MainLayoutScreen extends HookWidget {
  MainLayoutScreen({super.key});

  // TODO: Remove after API integration
  final User user = User(
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.admin,
  );

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = useState<int>(0);

    final tabs = [
      {
        'title': dashboardTab,
        'content': _buildDashboardContent(),
        'icon': Icons.dashboard_outlined,
      },
      {
        'title': brokersTab,
        'content': const Center(child: Text('Brokers Content')),
        'icon': Icons.business_outlined,
      },
      {
        'title': agentsTab,
        'content': _buildAgentsContent(),
        'icon': Icons.people_outline,
      },
      {
        'title': salesTab,
        'content': _buildSalesContent(),
        'icon': Icons.trending_up_outlined,
      },
      {
        'title': commissionTab,
        'content': const Center(child: Text('Commission Content')),
        'icon': Icons.account_balance_wallet_outlined,
      },
      {
        'title': reportsTab,
        'content': const Center(child: Text('Reports Content')),
        'icon': Icons.assessment_outlined,
      },
    ];

    void onTabSelected(int index) {
      selectedTabIndex.value = index;
    }

    final bool isSmallMobile = Responsive.isSmallMobile(context);

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: Responsive.showDrawer(context)
          ? MobileDrawer(
              user: user,
              selectedTab: ValueNotifier(
                tabs[selectedTabIndex.value]['title'] as String,
              ),
              selectedTabIndex: selectedTabIndex.value,
              onTabSelected: onTabSelected,
              tabs: tabs,
            )
          : null,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isSmallMobile ? 12 : webLayoutmargin,
            ),
            child: Column(
              children: [
                // Fixed Header
                Header(
                  tabs: tabs,
                  selectedTabIndex: selectedTabIndex.value,
                  onTabSelected: onTabSelected,
                  user: user,
                ),
                const SizedBox(height: defaultPadding),

                // Fixed Breadcrumb
                BreadCrumbNavigation(
                  hierarchyPath: [
                    dashboardAdmin,
                    tabs[selectedTabIndex.value]['title'] as String,
                  ],
                  onNavigate: (int navigationIndex) {},
                ),
                const SizedBox(height: defaultPadding),

                // Dynamic Content Area - Now scrollable with the entire page
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0.1, 0),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                  child: Container(
                    key: ValueKey(selectedTabIndex.value),
                    child: tabs[selectedTabIndex.value]['content'] as Widget,
                  ),
                ),

                // Fixed Footer
                const Footer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent() {
    return Builder(
      builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _welcomeUser(),
            const SizedBox(height: defaultPadding / 2),
            DashboardContent(),
            const SizedBox(height: defaultPadding),
          ],
        );
      },
    );
  }

  Widget _welcomeUser() {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.regularTextStyle(
            22,
            color: AppTheme.primaryTextColor.withOpacity(0.7),
          ),
          children: [
            TextSpan(
              text: 'Nabil',
              style: AppFonts.semiBoldTextStyle(
                22,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentsContent() {
    return Builder(
      builder: (context) {
        return const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AgentsTable(),
            SizedBox(height: defaultPadding),
          ],
        );
      },
    );
  }

  Widget _buildSalesContent() {
    return Builder(
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isMobile(context) ? 8 : webLayoutmargin,
            vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
          ),
          child: SalesReviewDocScreen(enableEditing: false),
        );
      },
    );
  }
}
