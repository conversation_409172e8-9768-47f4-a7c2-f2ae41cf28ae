import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/shared/components/breadcrumb_navigation.dart';
import '/src/presentation/screens/agent/components/agents_table.dart';
import '/src/presentation/screens/sales/sales_review_doc_screen.dart';
import '/src/presentation/screens/dashboard/components/mobile_drawer.dart';

import '../../core/config/app_strings.dart';
import '../../core/config/constants.dart';
import '../../core/config/responsive.dart';
import '../../core/theme/app_fonts.dart';
import '../../core/theme/app_theme.dart';
import '../../core/enum/user_role.dart';
import '../../domain/models/user.dart';
import 'dashboard/components/dashboard_content.dart';

class MainLayoutScreen extends HookWidget {
  MainLayoutScreen({super.key});

  // TODO: Remove after API integration
  final User user = User(
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$iconAssetpath/agent_round.png",
    role: UserRole.admin,
  );

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = useState<int>(0);

    final tabs = [
      {'title': dashboardTab, 'content': _buildDashboardContent()},
      {
        'title': brokersTab,
        'content': const Center(child: Text('Brokers Content')),
      },
      {'title': agentsTab, 'content': _buildAgentsContent()},
      {'title': salesTab, 'content': _buildSalesContent()},
      {
        'title': commissionTab,
        'content': const Center(child: Text('Commission Content')),
      },
      {
        'title': reportsTab,
        'content': const Center(child: Text('Reports Content')),
      },
    ];

    void onTabSelected(int index) {
      selectedTabIndex.value = index;
    }

    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      drawer: Responsive.showDrawer(context)
          ? MobileDrawer(
              user: user,
              selectedTab: ValueNotifier(
                tabs[selectedTabIndex.value]['title'] as String,
              ),
            )
          : null,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: isMobile ? 8 : webLayoutmargin,
            ),
            child: Column(
              children: [
                // Fixed Header with Header() styling
                Container(
                  margin: EdgeInsets.symmetric(
                    horizontal: Responsive.isMobile(context)
                        ? 8
                        : webLayoutmargin,
                    vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
                  ),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: defaultPadding / 2,
                      vertical: defaultPadding / 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.all(Radius.circular(12)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.15),
                          spreadRadius: 0,
                          blurRadius: 2,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Mobile menu button
                        if (Responsive.showDrawer(context))
                          IconButton(
                            icon: const Icon(Icons.menu),
                            onPressed: () {
                              Scaffold.of(context).openDrawer();
                            },
                          ),
                        const SizedBox(width: 8),

                        // Logo
                        Image.asset(
                          '$launcherAssetpath/logo.png',
                          scale: (154 / 40),
                        ),

                        // Desktop navigation items
                        if (!Responsive.showDrawer(context)) ...[
                          const SizedBox(width: 20),
                          ...tabs.asMap().entries.map((entry) {
                            final index = entry.key;
                            final tab = entry.value;
                            // Skip reports tab for non-platform owners
                            if (tab['title'] == reportsTab &&
                                user.role != UserRole.platformOwner) {
                              return const SizedBox.shrink();
                            }
                            return _buildNavItem(
                              context,
                              tab['title'] as String,
                              isSelected: selectedTabIndex.value == index,
                              onTap: () => onTabSelected(index),
                            );
                          }),

                          const Spacer(),

                          // Add New Button
                          if (user.role != UserRole.platformOwner)
                            _buildAddNewBtn(context),
                          const SizedBox(width: defaultPadding),

                          _headerIcon(Icons.notifications_outlined),
                          const SizedBox(width: defaultPadding),
                          _headerIcon(Icons.settings_outlined),
                          const SizedBox(width: defaultPadding),
                        ],

                        // Desktop profile info
                        if (!Responsive.isMobile(context)) ...[
                          _buildProfileInfo(context),
                        ],

                        // Mobile profile (simplified)
                        if (Responsive.isMobile(context)) ...[
                          const SizedBox(width: defaultPadding),
                          CircleAvatar(
                            backgroundImage: AssetImage(user.image),
                            radius: 16,
                          ),
                          const SizedBox(width: 8),
                        ],
                      ],
                    ),
                  ),
                ),

                // Fixed Breadcrumb
                BreadCrumbNavigation(
                  hierarchyPath: [
                    dashboardAdmin,
                    tabs[selectedTabIndex.value]['title'] as String,
                  ],
                  onNavigate: (int navigationIndex) {},
                ),

                // Dynamic Content Area - Now scrollable with the entire page
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0.1, 0),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                  child: Container(
                    key: ValueKey(selectedTabIndex.value),
                    child: tabs[selectedTabIndex.value]['content'] as Widget,
                  ),
                ),

                // Fixed Footer
                const Footer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _headerIcon(IconData icon) {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.headerIconBgColor,
        shape: BoxShape.circle,
      ),
      child: Padding(padding: const EdgeInsets.all(5.0), child: Icon(icon)),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor)
                    : AppFonts.mediumTextStyle(14, color: Colors.black),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewBtn(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
      ),
      onPressed: () {
        Navigator.pushNamed(context, '/register-broker');
      },
      icon: const Icon(Icons.add),
      label: const Text(addNewButton),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(backgroundImage: AssetImage(user.image)),
        const SizedBox(width: defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                userRoleToString(user.role),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        if (Responsive.isDesktop(context))
          const Icon(Icons.keyboard_arrow_down),
      ],
    );
  }

  Widget _buildDashboardContent() {
    return Builder(
      builder: (context) {
        final bool isMobile = Responsive.isMobile(context);
        return Container(
          padding: EdgeInsets.symmetric(vertical: isMobile ? 8 : defaultMargin),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _welcomeUser(),
              const SizedBox(height: defaultPadding / 2),
              DashboardContent(),
              const SizedBox(height: defaultPadding),
            ],
          ),
        );
      },
    );
  }

  Widget _welcomeUser() {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        textAlign: TextAlign.start,
        text: TextSpan(
          text: welcomeLabel,
          style: AppFonts.regularTextStyle(
            22,
            color: AppTheme.primaryTextColor.withOpacity(0.7),
          ),
          children: [
            TextSpan(
              text: 'Nabil',
              style: AppFonts.semiBoldTextStyle(
                22,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentsContent() {
    return Builder(
      builder: (context) {
        return Container(
          padding: EdgeInsets.fromLTRB(
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            Responsive.isMobile(context) ? 8 : defaultMargin,
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            defaultMargin,
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: defaultPadding),
              AgentsTable(),
              SizedBox(height: defaultPadding),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSalesContent() {
    return Builder(
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isMobile(context) ? 8 : webLayoutmargin,
            vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
          ),
          child: SalesReviewDocScreen(enableEditing: false),
        );
      },
    );
  }
}
