import 'package:flutter/material.dart';
import '../../../core/config/constants.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class BreadCrumbNavigation extends StatelessWidget {
  final List<String> hierarchyPath;

  final Function(int) onNavigate;

  const BreadCrumbNavigation({
    super.key,
    required this.hierarchyPath,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        // color: Colors.amber,
        width: MediaQuery.sizeOf(context).width,
        alignment: Alignment.centerLeft,
        // padding: const EdgeInsets.symmetric(horizontal: defaultPadding * 2),
        child: Row(children: _buildBreadcrumbItems()),
      ),
    );
  }

  List<Widget> _buildBreadcrumbItems() {
    List<Widget> items = [];
    // items.add();
    for (int i = 0; i < hierarchyPath.length; i++) {
      final isLast = i == hierarchyPath.length - 1;

      if (i > 0) items.add(_arrowWidget());
      items.add(_heirarchyItem(isLast, i, hierarchyPath[i]));
    }
    return items;
  }

  GestureDetector _heirarchyItem(bool isLast, int i, String name) {
    return GestureDetector(
      onTap: isLast || i < 0 ? null : () => onNavigate(i),
      child: Container(
        decoration: BoxDecoration(
          color: isLast ? AppTheme.breadcrumbBgColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            if (i == 0) ...[
              Image.asset(
                '$iconAssetpath/home_breadcrumb.png',
                height: 16,
                width: 16,
              ),
              SizedBox(width: 8),
            ],

            Container(
              padding: EdgeInsets.symmetric(
                horizontal: i == 0 ? 0 : 10,
                vertical: 5,
              ),
              child: Text(
                name,
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(
                  12,
                  color: isLast
                      ? AppTheme.breadcrumbChildTextColor
                      : AppTheme.breadcrumbParentTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _arrowWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Image.asset(
        '$iconAssetpath/arrow_right.png',
        height: 16,
        width: 16,
        color: AppTheme.breadcrumbArrowColor,
      ),
    );
  }
}
