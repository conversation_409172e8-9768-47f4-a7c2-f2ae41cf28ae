import 'package:flutter/material.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/enum/user_role.dart';
import '../../../domain/models/user.dart';

class Header extends StatelessWidget {
  final List<Map<String, dynamic>> tabs;
  final int selectedTabIndex;
  final Function(int) onTabSelected;
  final User user;
  final VoidCallback? onMenuPressed;
  final VoidCallback? onAddNewPressed;
  final VoidCallback? onNotificationPressed;
  final VoidCallback? onSettingsPressed;

  const Header({
    super.key,
    required this.tabs,
    required this.selectedTabIndex,
    required this.onTabSelected,
    required this.user,
    this.onMenuPressed,
    this.onAddNewPressed,
    this.onNotificationPressed,
    this.onSettingsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: Responsive.isMobile(context) ? 8 : defaultMargin,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              spreadRadius: 0,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Mobile menu button
            if (Responsive.showDrawer(context))
              IconButton(
                icon: const Icon(Icons.menu),
                onPressed:
                    onMenuPressed ??
                    () {
                      Scaffold.of(context).openDrawer();
                    },
              ),
            const SizedBox(width: 8),

            // Logo
            Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),

            // Desktop navigation items
            if (!Responsive.showDrawer(context)) ...[
              const SizedBox(width: 20),
              ...tabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tab = entry.value;
                // Skip reports tab for non-platform owners
                if (tab['title'] == reportsTab &&
                    user.role != UserRole.platformOwner) {
                  return const SizedBox.shrink();
                }
                return _buildNavItem(
                  context,
                  tab['title'] as String,
                  isSelected: selectedTabIndex == index,
                  onTap: () => onTabSelected(index),
                );
              }),

              const Spacer(),

              // Add New Button
              if (user.role != UserRole.platformOwner) _buildAddNewBtn(context),
              const SizedBox(width: defaultPadding),

              _headerIcon(
                Icons.notifications_outlined,
                onPressed: onNotificationPressed,
              ),
              const SizedBox(width: defaultPadding),
              _headerIcon(
                Icons.settings_outlined,
                onPressed: onSettingsPressed,
              ),
              const SizedBox(width: defaultPadding),
            ],

            // Desktop profile info
            if (!Responsive.isMobile(context)) ...[_buildProfileInfo(context)],

            // Mobile profile (simplified)
            if (Responsive.isMobile(context)) ...[
              const SizedBox(width: defaultPadding),
              CircleAvatar(backgroundImage: AssetImage(user.image), radius: 16),
              const SizedBox(width: 8),
            ],
          ],
        ),
      ),
    );
  }

  Widget _headerIcon(IconData icon, {VoidCallback? onPressed}) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: const BoxDecoration(
          color: AppTheme.headerIconBgColor,
          shape: BoxShape.circle,
        ),
        child: Padding(padding: const EdgeInsets.all(5.0), child: Icon(icon)),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    final bool isTablet = Responsive.isTablet(context);
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: AP,
              ),
              if (isSelected)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  height: 2,
                  width: 20,
                  color: AppTheme.primaryColor,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewBtn(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
      ),
      onPressed:
          onAddNewPressed ??
          () {
            Navigator.pushNamed(context, '/register-broker');
          },
      icon: const Icon(Icons.add),
      label: const Text(addNewButton),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(backgroundImage: AssetImage(user.image)),
        const SizedBox(width: defaultPadding / 2),
        if (Responsive.isDesktop(context))
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                userRoleToString(user.role),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        if (Responsive.isDesktop(context))
          const Icon(Icons.keyboard_arrow_down),
      ],
    );
  }
}
