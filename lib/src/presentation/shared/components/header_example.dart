import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'header.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/enum/user_role.dart';
import '../../../domain/models/user.dart';
import '../../../core/config/constants.dart';

/// Example of how to use the reusable Header component
class HeaderExampleScreen extends HookWidget {
  const HeaderExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedTabIndex = useState<int>(0);

    // Sample user data
    final User user = User(
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "1234567890",
      image: "$iconAssetpath/agent_round.png",
      role: UserRole.admin,
    );

    // Sample tabs data
    final List<Map<String, dynamic>> tabs = [
      {'title': dashboardTab, 'content': const Center(child: Text('Dashboard Content'))},
      {'title': agentsTab, 'content': const Center(child: Text('Agents Content'))},
      {'title': salesTab, 'content': const Center(child: Text('Sales Content'))},
      {'title': reportsTab, 'content': const Center(child: Text('Reports Content'))},
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Header Component Example'),
      ),
      body: Column(
        children: [
          // Using the reusable Header component
          Header(
            tabs: tabs,
            selectedTabIndex: selectedTabIndex.value,
            onTabSelected: (index) {
              selectedTabIndex.value = index;
            },
            user: user,
            // Optional callback handlers
            onAddNewPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Add New button pressed!')),
              );
            },
            onNotificationPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notification button pressed!')),
              );
            },
            onSettingsPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Settings button pressed!')),
              );
            },
          ),
          
          // Content area
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: tabs[selectedTabIndex.value]['content'] as Widget,
            ),
          ),
        ],
      ),
    );
  }
}

/// Usage Instructions:
/// 
/// 1. Import the Header component:
///    ```dart
///    import 'path/to/header.dart';
///    ```
/// 
/// 2. Create your tabs data structure:
///    ```dart
///    final List<Map<String, dynamic>> tabs = [
///      {'title': 'Dashboard', 'content': YourDashboardWidget()},
///      {'title': 'Users', 'content': YourUsersWidget()},
///      // ... more tabs
///    ];
///    ```
/// 
/// 3. Create a user object:
///    ```dart
///    final User user = User(
///      name: "User Name",
///      email: "<EMAIL>",
///      phone: "1234567890",
///      image: "path/to/image.png",
///      role: UserRole.admin,
///    );
///    ```
/// 
/// 4. Use the Header component:
///    ```dart
///    Header(
///      tabs: tabs,
///      selectedTabIndex: selectedTabIndex.value,
///      onTabSelected: (index) => selectedTabIndex.value = index,
///      user: user,
///      // Optional callbacks
///      onAddNewPressed: () => handleAddNew(),
///      onNotificationPressed: () => handleNotification(),
///      onSettingsPressed: () => handleSettings(),
///    )
///    ```
/// 
/// Features:
/// - Responsive design (mobile/tablet/desktop)
/// - Automatic drawer menu for mobile devices
/// - Role-based tab visibility (reports tab hidden for non-platform owners)
/// - Customizable callback handlers for buttons
/// - Consistent styling with app theme
/// - Logo display
/// - Profile information display
/// - Navigation indicators for selected tabs
